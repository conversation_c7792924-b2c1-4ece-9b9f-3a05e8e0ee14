import 'dart:developer';

import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/controllers/user_controller.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/model/address_model/address_model/address_model.dart';
import 'package:rapsap/model/address_model/address_model/datum.dart';
import 'package:rapsap/services/accountservices.dart';
import 'package:geocoding/geocoding.dart';

import '../model/order/order_list_model/order_list_model/order_list_model.dart';
import '../model/user_model/usermodel.dart';
import '../services/databaseHelper.dart';
import '../view/screens/AddressPage/myaddress.dart';
import '../view/widgets/loadingscreen.dart';
import 'ordercontroller.dart';

class AccountController extends GetxController {
  final UserController userController = Get.find<UserController>();

  Rx<AddressModel> addressModel = AddressModel().obs;
  Rx<Datum> selectedAddress = Datum().obs;
  RxBool loading = false.obs;
  RxBool buttonloading = false.obs;
  Future<AddressModel?> getaddress() async {
    log("function called ");
    if (userController.userdata.value.data != null) {
      final result = await AccountService.getAddress(
          {'user_id': userController.userdata.value.data!.id});
      addressModel.value = result!;

      log("addres:${addressModel.value.data}");
      if (addressModel.value.data != null) {
        if (addressModel.value.data!.isNotEmpty) {
          selectedAddress.value = result.data![
              result.data!.indexWhere((element) => element.isDefault == 1) == -1
                  ? 0
                  : result.data!
                      .indexWhere((element) => element.isDefault == 1)];
        }
      }

      log(result);
      return result;
    }
    return null;
  }

  @override
  void onInit() {
    if (userController.userdata.value.data?.id != null) {
      getaddress();
    }
    // TODO: implement onInit
    super.onInit();
  }

  Future logoutUser() async {
    Get.to(() => const LoadingScreen(
          text: 'Logging out',
        ));
    cleardata();
    Future.delayed(const Duration(seconds: 1),
        () => Get.offAll(() => const LoginScreen()));
    storage.erase();
  }
}

cleardata() {
  final accountController = Get.isRegistered<AccountController>()
      ? Get.find<AccountController>()
      : Get.put(AccountController());
  final UserController userController = Get.find<UserController>();

  GetStorage cartstorage = GetStorage('cart');
  cartstorage.erase();
  final cartController = Get.find<CartController>();
  final OrderController orderController = Get.find<OrderController>();

  userController.locationaddress.value = const Placemark();
  userController.locationpoint.value.clear();
  userController.loginstatus.value = false;
  userController.userdata.value = const UserData();

  DatabaseHelper.instance.clearAllOrders();
  DatabaseHelper.instance.clearShopingCart();
  DatabaseHelper.instance.clearPayment();

  accountController.addressModel.value = AddressModel();
  cartController.myCartItems.value.clear();
  orderController.orderListModel.value = OrderListModel();

  selectedAddress = Datum();
}
