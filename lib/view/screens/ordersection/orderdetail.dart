import 'dart:io';

import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:Rapsap/controllers/ordercontroller.dart';
import 'package:Rapsap/services/orderservices.dart';
import 'package:Rapsap/view/screens/ordersection/viewproducts.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widgets/custom.dart';
import '../Home_screen/home_screen.dart';
import '../cart_screen/cart_screen.dart';

class OrderDetailScreen extends StatelessWidget {
  OrderDetailScreen({Key? key, this.orderId}) : super(key: key);
  final OrderController orderController = Get.find<OrderController>();
  final orderId;
  String reasonText = "";

  @override
  Widget build(BuildContext context) {
    orderController.getOrderbyId(orderId);
    print("orderid:$orderId");

    return Scaffold(
        backgroundColor: kwhite,
        appBar: AppBar(
          backgroundColor: kwhite,
          leadingWidth: 0,
          elevation: 0,
          toolbarHeight: 60,
          automaticallyImplyLeading: false,
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  InkWell(
                    onTap: () => Get.back(),
                    child: const Icon(
                      Icons.arrow_back,
                      color: kblue,
                    ),
                  ),
                  const SizedBox(width: 27),
                  SizedBox(
                    width: Get.width * 0.5,
                    child: Text(
                      "Order Details",
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        color: Colors.black,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                width: 50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                        onTap: (() async {
                          if (!await launchUrl(Uri.parse(url()))) {
                            throw "Could not launch whatsapp";
                          }
                        }),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SizedBox(
                              width: 30,
                              child: getSvgIcon(
                                  "assets/svg/customer-support.svg")),
                        )),
                  ],
                ),
              )
            ],
          ),
          bottom: const PreferredSize(
              preferredSize: Size.fromHeight(10), child: ThickDivider()),
        ),
        body: Obx(
          () => orderController.orderDetailModel.value.data == null
              ? const Center(
                  child: CircularProgressIndicator.adaptive(
                    strokeWidth: 4,
                    backgroundColor: kgrey,
                    valueColor: AlwaysStoppedAnimation<Color>(kblack),
                  ),
                )
              : SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          kheight10,
                          Text(
                            'Order ID :  RAPS-${orderController.orderDetailModel.value.data!.orderId}',
                            style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                color: kblack.withOpacity(0.7)),
                          ),
                          kheight10,
                        ],
                      ).paddingSymmetric(horizontal: defaultpadding),
                      const ThickDivider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Ordered Product (${orderController.orderDetailModel.value.data!.orderDetails!.length} item)',
                            style: const TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 16),
                          ),
                          InkWell(
                            onTap: () {
                              Get.to(() => ViewProducts(
                                    listitems: orderController.orderDetailModel
                                        .value.data!.orderDetails!,
                                  ));
                            },
                            child: Container(
                                decoration: BoxDecoration(
                                    border: Border.all(color: kblue)),
                                child: const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text(
                                    'View',
                                    style: TextStyle(
                                        color: kblue,
                                        fontWeight: FontWeight.w500),
                                  ),
                                )),
                          )
                        ],
                      ).paddingSymmetric(
                          horizontal: defaultpadding, vertical: 14),
                      const ThickDivider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: defaultpadding),
                        child: ListView(
                          primary: false,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            TimelineTile(
                              // alignment: TimelineAlign.manual,
                              lineXY: 0.1,
                              isFirst: true,
                              indicatorStyle: IndicatorStyle(
                                width: 40,
                                height: 40,
                                color: kblue,
                                padding: const EdgeInsets.all(2),
                                indicator: SvgPicture.asset(
                                  "assets/svg/${!isChecked('order_created') ? 'order-placed-pending' : 'order-placed-done'}.svg",
                                  fit: BoxFit.scaleDown,
                                ),
                              ),
                              endChild: _RightChild(
                                // asset: 'images/coin.png',
                                title: 'Order Placed',

                                message: isChecked('order_created')
                                    ? 'Order placed at ${getDate(orderController.orderDetailModel.value.data!.createdAt.toString())}'
                                    : 'Pending',
                              ),
                              beforeLineStyle: LineStyle(
                                color: isChecked('order_created')
                                    ? kblue
                                    : Colors.grey[300]!,
                              ),
                            ),
                            isChecked('order_cancelled') &&
                                    !isChecked('order_paid')
                                ? const SizedBox()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: kblue,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!isChecked('order_paid') ? 'order-confirmed-pending' : 'order-confirmed-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: _RightChild(
                                      title: 'Order confirmed',
                                      disabled: false,
                                      message: isChecked('order_paid')
                                          ? 'Your order is confirmed and will be delivered soon.'
                                          : 'Pending',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: isChecked('order_paid')
                                          ? kblue
                                          : Colors.grey[300]!,
                                    ),
                                  ),
                            isChecked('order_cancelled') && !isChecked('packed')
                                ? const SizedBox()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: kblue,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!isChecked('packed') ? 'order-shipped-pending' : 'order-shipped-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: _RightChild(
                                      title: 'Order Packed',
                                      disabled: !isChecked('packed'),
                                      message: isChecked('packed')
                                          ? 'Your order has been packed.'
                                          : 'Pending',
                                      //  singleOrder.data!.orderLogs!.any(
                                      //         (element) => element.status == 'ARRIVED')
                                      //     ? 'Your order Shipped at ${singleOrder.data!.orderLogs!.where((element) => element.status == 'ARRIVED').first.meta.arrivalTime}'
                                      //     : 'Pending',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: orderController.orderDetailModel
                                              .value.data!.orderLogs!
                                              .any((element) =>
                                                  element.status == 'packed')
                                          ? kblue
                                          : Colors.grey[300]!,
                                    ),
                                    // afterLineStyle: const LineStyle(
                                    //   color: Colors.red,
                                    // ),
                                  ),
                            isChecked('order_cancelled') &&
                                    !isChecked('out_for_delivery') &&
                                    !isChecked('rescheduled')
                                ? const SizedBox()
                                : Row(
                                    children: [
                                      Expanded(
                                        child: TimelineTile(
                                          // alignment: TimelineAlign.manual,
                                          lineXY: 0.1,
                                          indicatorStyle: IndicatorStyle(
                                            width: 40,
                                            height: 40,
                                            color: kblue,
                                            padding: const EdgeInsets.all(2),
                                            indicator: SvgPicture.asset(
                                              "assets/svg/${!isChecked('out_for_delivery') ? 'out-for-delivery-pending' : 'out-for-delivery-done'}.svg",
                                              fit: BoxFit.scaleDown,
                                            ),
                                          ),
                                          endChild: _RightChild(
                                            disabled:
                                                !isChecked('out_for_delivery'),
                                            title: 'Out for Delivery',
                                            message: orderController
                                                        .orderDetailModel
                                                        .value
                                                        .data!
                                                        .orderLogs!
                                                        .any((element) =>
                                                            element.status ==
                                                            'reschedule') &&
                                                    orderController
                                                            .orderDetailModel
                                                            .value
                                                            .data!
                                                            .orderStatus ==
                                                        "reschedule"
                                                ? 'Order Rescheduled'
                                                : orderController
                                                        .orderDetailModel
                                                        .value
                                                        .data!
                                                        .orderLogs!
                                                        .any((element) =>
                                                            element.status ==
                                                            'out_for_delivery')
                                                    ? '${orderController.orderDetailModel.value.data!.orderLogs!.where((element) => element.status == 'out_for_delivery').first.meta!.riderName!} - Mob: ${orderController.orderDetailModel.value.data!.orderLogs!.where((element) => element.status == 'out_for_delivery').first.meta!.riderContact}'
                                                    : 'Pending',
                                          ),
                                          beforeLineStyle: LineStyle(
                                            color: isChecked('out_for_delivery')
                                                ? kblue
                                                : Colors.grey[300]!,
                                          ),
                                        ),
                                      ),
                                      orderController.orderDetailModel.value
                                                      .data!.orderStatus ==
                                                  "out_for_delivery" ||
                                              orderController
                                                      .orderDetailModel
                                                      .value
                                                      .data!
                                                      .orderStatus ==
                                                  "arrived"
                                          ? IconButton(
                                              onPressed: () async {
                                                if (!await launchUrl(Uri.parse(
                                                    "tel: ${orderController.orderDetailModel.value.data!.orderLogs!.where((element) => element.status == 'out_for_delivery').first.meta!.riderContact.toString()}"))) {
                                                  throw "Could not launch https://rapsap.com/terms.html";
                                                }
                                              },
                                              icon: const Icon(Icons.phone))
                                          : const SizedBox()
                                    ],
                                  ),
                            isChecked('order_cancelled') &&
                                    !isChecked('delivered')
                                ? const SizedBox()
                                : TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    isLast: true,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 40,
                                      color: kblue,
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/${!isChecked('delivered') ? 'order-deliverd-pending' : 'order-deliverd-done'}.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: _RightChild(
                                      disabled: !isChecked('delivered'),
                                      title: 'Order Delivered',
                                      message: orderController.orderDetailModel
                                              .value.data!.orderLogs!
                                              .any((element) =>
                                                  element.status == 'delivered')
                                          ? 'delivered'
                                          : 'Pending',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: isChecked('delivered')
                                          ? kblue
                                          : Colors.grey[300]!,
                                    ),
                                  ),
                            orderController
                                    .orderDetailModel.value.data!.orderLogs!
                                    .where((element) =>
                                        element.status == 'order_cancelled')
                                    .isNotEmpty
                                ? TimelineTile(
                                    // alignment: TimelineAlign.manual,
                                    lineXY: 0.1,
                                    isLast: true,
                                    indicatorStyle: IndicatorStyle(
                                      width: 40,
                                      height: 30,
                                      color: Colors.red.withOpacity(0.5),
                                      padding: const EdgeInsets.all(2),
                                      indicator: SvgPicture.asset(
                                        "assets/svg/order-cancelled-done.svg",
                                        fit: BoxFit.scaleDown,
                                      ),
                                    ),
                                    endChild: const _RightChild(
                                      title: 'Order Cancelled',
                                      message: 'CANCELLED',
                                    ),
                                    beforeLineStyle: LineStyle(
                                      color: Colors.red.withOpacity(0.5),
                                    ),
                                  )
                                : const SizedBox(),
                          ],
                        ),
                      ),
                      const ThickDivider(),
                      kheight10,
                      canCancel(orderController
                              .orderDetailModel.value.data!.orderStatus)
                          ? const SizedBox()
                          : InkWell(
                              onTap: (() {
                                Get.to(() => CancelScreen());
                              }),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: const [
                                      Text(
                                        'Cancel Order',
                                        style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600),
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 15,
                                      )
                                    ],
                                  ).paddingSymmetric(
                                      horizontal: defaultpadding),
                                  kheight10,
                                  const ThickDivider(),
                                ],
                              )),
                      kheight10,
                      orderController
                                  .orderDetailModel.value.data!.orderStatus ==
                              'DISPATCHED'
                          ? Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        const Text(
                                          'Delivery Agent :  ',
                                          style: TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          orderController.orderDetailModel.value
                                                  .data!.orderLogs!
                                                  .where((element) =>
                                                      element.status ==
                                                      'DISPATCHED')
                                                  .first
                                                  .meta
                                                  ?.riderName ??
                                              "",
                                          style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ],
                                    ),
                                    const Icon(
                                      Icons.phone_outlined,
                                      size: 22,
                                      color: Colors.green,
                                    ),
                                  ],
                                ).paddingSymmetric(horizontal: defaultpadding),
                                kheight10,
                              ],
                            )
                          : const SizedBox(),
                      orderController.orderDetailModel.value.data!.refund ==
                              null
                          ? const SizedBox()
                          : Container(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Refund Details',
                                    style: TextStyle(
                                        color: kblack,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600),
                                  ).paddingSymmetric(
                                      horizontal: defaultpadding),
                                  ...List.generate(
                                      orderController.orderDetailModel.value
                                          .data!.refund!.length,
                                      (index) => Container(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                hgap(15),
                                                Text(
                                                  orderController
                                                              .orderDetailModel
                                                              .value
                                                              .data!
                                                              .refund![index]
                                                              .status ==
                                                          "processed"
                                                      ? "Refund Completed"
                                                      : orderController
                                                                  .orderDetailModel
                                                                  .value
                                                                  .data!
                                                                  .refund![
                                                                      index]
                                                                  .status ==
                                                              "failed"
                                                          ? "failed"
                                                          : "Initiated"
                                                              .toString(),
                                                  style: TextStyle(
                                                      color: orderController
                                                                  .orderDetailModel
                                                                  .value
                                                                  .data!
                                                                  .refund![
                                                                      index]
                                                                  .status ==
                                                              "failed"
                                                          ? Colors.red
                                                          : Colors.green,
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                                Text(
                                                  "₹ ${orderController.orderDetailModel.value.data!.refund![index].amount}  ${orderController.orderDetailModel.value.data!.refund![index].status == "processed" ? " has been refunded" : orderController.orderDetailModel.value.data!.refund![index].status == "refund request has been failed" ? "failed" : " has been initiated"} on ${getDate(orderController.orderDetailModel.value.data!.refund![index].updatedAt.toString())} ",
                                                  style: const TextStyle(
                                                      color: kblack,
                                                      fontSize: 15,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ).paddingSymmetric(
                                                    vertical: 10),
                                                const Divider()
                                              ],
                                            ).paddingSymmetric(horizontal: 16),
                                          )),
                                  const ThickDivider(),
                                ],
                              ),
                            ),
                      const Text(
                        'Delivery Address',
                        style: TextStyle(
                            color: kblack,
                            fontSize: 18,
                            fontWeight: FontWeight.w600),
                      ).paddingSymmetric(horizontal: defaultpadding),
                      kheight20,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                orderController.orderDetailModel.value.data!
                                        .deliveryAddress!.name ??
                                    "",
                                style: const TextStyle(
                                    fontWeight: FontWeight.w600),
                              ),
                              kwidth20,
                              Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: kblue.withOpacity(0.2)),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0, vertical: 4),
                                  child: Text(
                                    orderController.orderDetailModel.value.data!
                                            .deliveryAddress!.addressType ??
                                        "",
                                    style: const TextStyle(color: kblue),
                                  ),
                                ),
                              )
                            ],
                          ),
                          kheight5,
                          Text(
                            "${orderController.orderDetailModel.value.data!.deliveryAddress!.address1 ?? ""}, ${orderController.orderDetailModel.value.data!.deliveryAddress!.address2 ?? ""}, ${orderController.orderDetailModel.value.data!.deliveryAddress!.city ?? ""}, \n${orderController.orderDetailModel.value.data!.deliveryAddress!.state ?? ""}, ${orderController.orderDetailModel.value.data!.deliveryAddress!.pincode ?? ""}, \n${orderController.orderDetailModel.value.data!.deliveryAddress!.phone ?? ""}",
                            style: const TextStyle(color: Color(0xFF556F80)),
                            textAlign: TextAlign.start,
                          ),
                        ],
                      ).paddingSymmetric(horizontal: defaultpadding),
                      kheight10,
                      const ThickDivider(),
                      kheight20,
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: defaultpadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Price Details',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 18),
                            ),
                            kheight20,
                            Obx(() => PriceRowsWidget(
                                  amount:
                                      '₹ ${orderController.orderDetailModel.value.data!.subTotal}',
                                  title:
                                      'Price(${orderController.orderDetailModel.value.data!.orderDetails!.length} Items)',
                                )),
                            hgap(16),
                            PriceRowsWidget(
                              amount:
                                  '${orderController.orderDetailModel.value.data?.discount}',
                              title: 'Discount',
                            ),
                            hgap(16),
                            PriceRowsWidget(
                              amount:
                                  '${orderController.orderDetailModel.value.data?.deliveryCost ?? 0}',
                              title: 'Delivery fee',
                            ),
                            hgap(16),
                            const PriceRowsWidget(
                              amount: '0',
                              title: 'Taxes',
                            ),
                            Divider(
                              thickness: 1,
                              color: kblack.withOpacity(0.06),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Total Amount',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                Text(
                                  '₹${orderController.orderDetailModel.value.data?.grandTotal}',
                                  style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700),
                                ),
                              ],
                            ).paddingSymmetric(vertical: 20),
                            kheight20,
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        ));
  }

  String url() {
    if (Platform.isIOS) {
      return "whatsapp://wa.me/919867870802?text=Enquiry on RAPS-$orderId";
    } else {
      return "whatsapp://send?phone=919867870802&text=Enquiry on  RAPS-$orderId";
    }
  }

  isChecked(status) {
    return orderController.orderDetailModel.value.data!.orderLogs!
        .any((element) => element.status == status);
  }
}

// ignore: must_be_immutable
class CancelScreen extends StatelessWidget {
  CancelScreen({Key? key}) : super(key: key);
  String reasonText = "";
  final OrderController orderController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: kwhite,
        leadingWidth: 0,
        elevation: 0,
        toolbarHeight: 60,
        automaticallyImplyLeading: false,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => Get.back(),
                  child: const Icon(
                    Icons.arrow_back,
                    color: kblue,
                  ),
                ),
                const SizedBox(width: 27),
                SizedBox(
                  width: Get.width * 0.5,
                  child: Text(
                    "Cancel Order",
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.dmSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     getSvgIcon("assets/svg/cart-coverd.svg"),
            //     kwidth10,
            //   ],
            // )
          ],
        ),
        bottom: const PreferredSize(
            preferredSize: Size.fromHeight(10), child: ThickDivider()),
      ),
      body: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Container(
          height: MediaQuery.of(context).size.height / 1.5,
          color: Colors.white,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                const Align(
                    alignment: Alignment.topLeft,
                    child: Text('Reason for cancellation')),
                const SizedBox(height: 10),
                Container(
                    child: TextFormField(
                  minLines: 5,
                  maxLines: 5,
                  maxLength: 128,

                  decoration: const InputDecoration(
                    focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: kblack, width: 2.0),
                        borderRadius: BorderRadius.zero),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey, width: 2.0),
                      borderRadius: BorderRadius.zero,
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey, width: 2.0),
                      borderRadius: BorderRadius.all(
                        Radius.circular(19.0),
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Colors.redAccent, width: 2.0),
                      borderRadius: BorderRadius.all(
                        Radius.circular(19.0),
                      ),
                    ),
                    hintText: 'Type Reason here!',
                  ),
                  // The validator receives the text that the user has entered.
                  onChanged: (val) {
                    // setState(() {
                    reasonText = val;
                    // });
                  },
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return "reason required";
                    }
                    return null;
                  },
                )),
                kheight30,
                Row(
                  children: [
                    Expanded(
                      child: Obx(() => ElevatedButton(
                            onPressed: orderController.buttonloading.value
                                ? null
                                : () async {
                                    if (_formKey.currentState!.validate()) {
                                      orderController.buttonloading.value =
                                          true;
                                      await cancelOrderFn();
                                    }
                                  },
                            style: ElevatedButton.styleFrom(
                                elevation: 0,
                                primary: kblack,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 13, horizontal: 30)),
                            child: orderController.buttonloading.value
                                ? const SizedBox(
                                    height: 20,
                                    child: LoadingIndicator(
                                      colors: [kblue, kblack],
                                      indicatorType: Indicator.cubeTransition,
                                    ),
                                  )
                                : const Text(
                                    'Cancel Order',
                                    style: TextStyle(fontSize: 17),
                                  ),
                          )),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future cancelOrderFn() async {
    UserController usertCTRL = Get.find();
    var req = {
      'order_id': orderController.orderDetailModel.value.data?.orderId,
      'cancel_reason': reasonText,
      'cancelled_by': 'Customer',
      'updated_by': usertCTRL.userdata.value.data!.id,
    };
    var res = await OrderServices.cancelOrder(req);
    print('Cancel order res: $res');
    Get.offUntil(
        GetPageRoute(
            page: () => OrderDetailScreen(
                  orderId: orderController.orderDetailModel.value.data!.orderId,
                )),
        (route) => false);

    // getSingleOrder(singleOrder.data?.orderId);
  }
}

String? getDate(String date) {
  var dd = DateFormat('dd-MMM-yyyy hh:mm').format(DateTime.parse(date));

  return dd.toString();
}

class ThickDivider extends StatelessWidget {
  const ThickDivider({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 0,
      color: kblack.withOpacity(0.06),
      thickness: 4,
    );
  }
}

bool canCancel(status) {
  print('status $status');
  // return singleOrder.data!.orderLogs!
  // .any((element) => element.status == status);

  switch (status) {
    case 'order_placed':
    case 'order_updated':
    case 'order_paid':
      return false;

    case 'ARRIVED':
    case 'packed':
    case 'DISPATCHED':
    case 'delivered':
    case 'arrived':
    case 'reschedule':
    case 'out_for_delivery':
    case 'CANCELLED':
    case 'order_cancelled':
      return true;

    default:
      return false;
  }
}

class _RightChild extends StatelessWidget {
  const _RightChild({
    Key? key,
    this.title = '',
    this.message = '',
    this.disabled = false,
  }) : super(key: key);

  final String title;
  final String message;
  final bool disabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            title,
            style: const TextStyle(
              color: kblack,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            message,
            softWrap: true,
            style: TextStyle(
              color: kblack.withOpacity(0.5),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
